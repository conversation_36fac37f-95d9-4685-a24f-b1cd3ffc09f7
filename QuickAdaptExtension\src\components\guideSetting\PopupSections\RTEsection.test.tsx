import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import RTEsection from './RTEsection';

// Mock the store
const mockStore = {
  rtesContainer: [
    {
      id: 'test-container-1',
      rtes: [
        {
          id: 'test-rte-1',
          text: 'Test content'
        }
      ]
    }
  ],
  updateRTEContainer: jest.fn(),
  setIsUnSavedChanges: jest.fn(),
  cloneRTEContainer: jest.fn(),
  clearRteDetails: jest.fn(),
  selectedTemplate: 'Announcement',
  selectedTemplateTour: '',
  announcementGuideMetaData: [],
  toolTipGuideMetaData: [],
  handleAnnouncementRTEValue: jest.fn(),
  handleTooltipRTEValue: jest.fn(),
  createWithAI: false,
  currentStep: 1,
  ensureAnnouncementRTEContainer: jest.fn(() => [])
};

// Mock the store hook
jest.mock('../../../store/drawerStore', () => ({
  __esModule: true,
  default: () => mockStore
}));

// Mock JoditEditor
jest.mock('jodit-react', () => {
  return function MockJoditEditor({ value, onChange }: any) {
    return (
      <div 
        data-testid="jodit-editor"
        data-value={value}
        onClick={() => onChange && onChange('test content')}
      >
        {value}
      </div>
    );
  };
});

// Mock translation
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

describe('RTEsection', () => {
  const defaultProps = {
    textBoxRef: { current: null },
    guidePopUpRef: { current: null },
    isBanner: false,
    handleDeleteRTESection: jest.fn(),
    index: 0,
    onClone: jest.fn(),
    isCloneDisabled: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders RTEsection component', () => {
    render(<RTEsection {...defaultProps} />);
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });

  it('applies correct styles for Announcement template', () => {
    mockStore.selectedTemplate = 'Announcement';
    const { container } = render(<RTEsection {...defaultProps} />);
    
    // Check if the component has the correct class
    const rteContainer = container.querySelector('.qadpt-rte');
    expect(rteContainer).toBeInTheDocument();
    
    // The styles should be applied via sx prop, which would be in the DOM
    // We can verify the component renders without errors
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });

  it('applies correct styles for Tooltip template', () => {
    mockStore.selectedTemplate = 'Tooltip';
    const { container } = render(<RTEsection {...defaultProps} />);
    
    const rteContainer = container.querySelector('.qadpt-rte');
    expect(rteContainer).toBeInTheDocument();
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });

  it('applies correct styles for Banner template', () => {
    mockStore.selectedTemplate = 'Banner';
    const { container } = render(<RTEsection {...defaultProps} />);
    
    const rteContainer = container.querySelector('.qadpt-rte');
    expect(rteContainer).toBeInTheDocument();
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });

  it('applies correct styles for Tour with Announcement template', () => {
    mockStore.selectedTemplate = 'Tour';
    mockStore.selectedTemplateTour = 'Announcement';
    const { container } = render(<RTEsection {...defaultProps} />);
    
    const rteContainer = container.querySelector('.qadpt-rte');
    expect(rteContainer).toBeInTheDocument();
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });

  it('applies correct styles for Tour with Tooltip template', () => {
    mockStore.selectedTemplate = 'Tour';
    mockStore.selectedTemplateTour = 'Tooltip';
    const { container } = render(<RTEsection {...defaultProps} />);
    
    const rteContainer = container.querySelector('.qadpt-rte');
    expect(rteContainer).toBeInTheDocument();
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });

  it('applies correct styles for Tour with Banner template', () => {
    mockStore.selectedTemplate = 'Tour';
    mockStore.selectedTemplateTour = 'Banner';
    const { container } = render(<RTEsection {...defaultProps} />);
    
    const rteContainer = container.querySelector('.qadpt-rte');
    expect(rteContainer).toBeInTheDocument();
    expect(screen.getByTestId('jodit-editor')).toBeInTheDocument();
  });
});
