{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\RTE\\\\RTESection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useRef, memo, useMemo, useCallback } from \"react\";\nimport { Box, Tooltip, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  items: {\n    id,\n    style,\n    rteBoxValue,\n    placeholder\n  },\n  boxRef,\n  handleFocus,\n  handleeBlur,\n  isPopoverOpen,\n  setIsPopoverOpen,\n  currentRTEFocusedId,\n  toolbarVisibleRTEId,\n  setToolbarVisibleRTEId\n}, ref) => {\n  var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setIsUnSavedChanges,\n    setHtmlContent,\n    textvaluess,\n    setTextvaluess,\n    backgroundC,\n    setBackgroundC,\n    Bbordercolor,\n    BborderSize,\n    bpadding,\n    sectionColor,\n    setSectionColor,\n    handleTooltipRTEBlur,\n    handleTooltipRTEValue,\n    handleRTEDeleteSection,\n    handleRTECloneSection,\n    tooltip,\n    currentStep,\n    toolTipGuideMetaData\n  } = useDrawerStore(state => state);\n  // Removed unused state variables since we're using Jodit editor directly\n\n  // Toggle toolbar visibility function\n  const toggleToolbar = rteId => {\n    if (setToolbarVisibleRTEId) {\n      if (toolbarVisibleRTEId === rteId) {\n        setToolbarVisibleRTEId(null);\n        setIsPopoverOpen(false);\n      } else {\n        setToolbarVisibleRTEId(rteId);\n        setIsPopoverOpen(true);\n        handleFocus(rteId);\n      }\n    }\n  };\n\n  // Memoize Jodit config to prevent re-renders and focus loss\n  const joditConfig = useMemo(() => ({\n    readonly: false,\n    toolbar: toolbarVisibleRTEId === id,\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: false,\n    // Fix dialog positioning by setting popup root to document body\n    popupRoot: document.body,\n    // Ensure dialogs appear in correct position\n    zIndex: 100000,\n    globalFullSize: false,\n    // Add custom CSS to ensure text is visible\n    style: {\n      color: '#000000 !important',\n      backgroundColor: '#ffffff',\n      fontFamily: 'Poppins, sans-serif'\n    },\n    // Override editor styles to ensure text visibility\n    editorCssClass: 'jodit-tooltip-editor',\n    // Set default content styling\n    enter: 'p',\n    // Fix link dialog positioning\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    // Dialog configuration\n    dialog: {\n      zIndex: 100001\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [toolbarVisibleRTEId, id]);\n\n  // Memoize onChange handler to prevent re-renders\n  const handleContentChange = useCallback(newContent => {\n    handleTooltipRTEValue(id, newContent);\n  }, [id, handleTooltipRTEValue]);\n  const [isEditing, setIsEditing] = useState(false);\n  const editorRef = useRef(null);\n  const containerRef = useRef(null);\n\n  // const handleInput = () => {\n  // \t// Update the content state when user types\n  // \tif (boxRef.current) {\n  // \t\tconst updatedContent = boxRef.current.innerHTML;\n  // \t\tsetContent(updatedContent); // Store the content in state\n  // \t\tsetHtmlContent(updatedContent); // Update the HTML content\n  // \t\tsetIsUnSavedChanges(true);\n  // \t\tpreserveCaretPosition();\n  // \t}\n  // };\n  // Removed caret position functions since we're using Jodit editor\n\n  // useEffect(() => {\n  // \t// After content update, restore the cursor position\n  // \trestoreCaretPosition();\n  // }, [boxRef.current?.innerHTML]); // Run when content changes\n\n  // Remove section\n\n  // useEffect(() => {\n  // \tif (boxRef.current?.innerHTML?.trim()) {\n  // \t\tsetIsUnSavedChanges(true);\n  // \t}\n  // }, [boxRef.current?.innerHTML?.trim()]);\n\n  // Removed useEffect since we're using Jodit editor directly\n\n  // Auto-focus the editor when editing mode is activated\n  useEffect(() => {\n    if (isEditing && editorRef.current) {\n      setTimeout(() => {\n        editorRef.current.editor.focus();\n      }, 50);\n    }\n  }, [isEditing]);\n\n  // Handle clicks outside the editor to close editing mode\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Check if the target is inside the editor or related elements\n      if (containerRef.current && !containerRef.current.contains(event.target) &&\n      // Click outside the editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setIsEditing(false); // Close the editor if clicked outside\n      }\n    };\n    if (isEditing) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n      return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        position: \"relative\",\n        //padding: 0,\n        margin: 0,\n        boxSizing: \"border-box\",\n        transition: \"border 0.2s ease-in-out\",\n        backgroundColor: sectionColor || \"defaultColor\"\n        //border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\n        // padding: `${bpadding}px !important` || \"0\",\n      },\n      className: \"qadpt-rte\",\n      id: \"rte-box\",\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleRTEDeleteSection(id),\n            disabled: ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.length) === 1,\n            sx: {\n              \"&:hover\": {\n                backgroundColor: \"transparent !important\"\n              },\n              svg: {\n                path: {\n                  fill: \"var(--primarycolor)\"\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.length) === 1 ? 0.5 : 1,\n                pointerEvents: 'none',\n                height: \"24px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleRTECloneSection(id),\n            sx: {\n              \"&:hover\": {\n                backgroundColor: \"transparent !important\"\n              },\n              svg: {\n                height: \"24px\",\n                path: {\n                  fill: \"var(--primarycolor)\"\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.length) === 1 ? 0.5 : 1,\n                pointerEvents: 'none',\n                height: \"24px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true),\n        placement: \"top\",\n        slotProps: {\n          tooltip: {\n            sx: {\n              backgroundColor: \"white\",\n              color: \"black\",\n              borderRadius: \"4px\",\n              padding: '0px 4px',\n              border: \"1px dashed var(--primarycolor)\"\n            }\n          }\n        },\n        PopperProps: {\n          modifiers: [{\n            name: \"preventOverflow\",\n            options: {\n              boundary: \"viewport\" // Ensure tooltip doesn't go outside the viewport\n            }\n          }, {\n            name: \"flip\",\n            options: {\n              enabled: true\n            }\n          }]\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            position: \"relative\",\n            color: \"#000000\",\n            backgroundColor: \"#ffffff\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"style\", {\n            children: `\n\t\t\t\t\t\t\t\t\t/* Tooltip/Hotspot specific Jodit editor styles */\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg p {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg div {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg span {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg br {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Override any inherited styles from tooltip/modal */\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Ensure text is visible in all states */\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Override any modal or tooltip text color inheritance */\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg,\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg * {\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Fix Jodit dialog positioning - target correct classes */\n\t\t\t\t\t\t\t\t\t.jodit.jodit-dialog {\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t.jodit-dialog .jodit-dialog__panel {\n\t\t\t\t\t\t\t\t\t\tposition: relative !important;\n\t\t\t\t\t\t\t\t\t\ttop: auto !important;\n\t\t\t\t\t\t\t\t\t\tleft: auto !important;\n\t\t\t\t\t\t\t\t\t\ttransform: none !important;\n\t\t\t\t\t\t\t\t\t\tmax-width: 400px !important;\n\t\t\t\t\t\t\t\t\t\tbackground: white !important;\n\t\t\t\t\t\t\t\t\t\tborder: 1px solid #ccc !important;\n\t\t\t\t\t\t\t\t\t\tborder-radius: 4px !important;\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/* Fix for link dialog specifically */\n\t\t\t\t\t\t\t\t\t.jodit-dialog_alert {\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n            value: rteBoxValue || \"\",\n            config: joditConfig,\n            onChange: handleContentChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: e => {\n              e.stopPropagation();\n              toggleToolbar(id);\n            },\n            sx: {\n              position: \"absolute\",\n              bottom: \"2px\",\n              right: \"2px\",\n              width: \"24px\",\n              height: \"24px\",\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n              zIndex: 1000,\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\"\n              },\n              \"& svg\": {\n                width: \"16px\",\n                height: \"16px\"\n              }\n            },\n            title: translate(\"Toggle Toolbar\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: editicon\n              },\n              style: {\n                height: '16px',\n                width: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}, \"VWw4tdVi33JBr5Pgq5YTLXH9pt4=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"VWw4tdVi33JBr5Pgq5YTLXH9pt4=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default _c3 = /*#__PURE__*/memo(RTEsection);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useRef", "memo", "useMemo", "useCallback", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "items", "id", "style", "rteBoxValue", "placeholder", "boxRef", "handleFocus", "handleeBlur", "isPopoverOpen", "setIsPopoverOpen", "currentRTEFocusedId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "ref", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "t", "translate", "setIsUnSavedChanges", "setHtmlContent", "textvaluess", "setTextvaluess", "backgroundC", "setBackgroundC", "Bbordercolor", "BborderSize", "bpadding", "sectionColor", "setSectionColor", "handleTooltipRTEBlur", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "currentStep", "toolTipGuideMetaData", "state", "toggleToolbar", "rteId", "joditConfig", "readonly", "toolbar", "toolbarSticky", "toolbarAdaptive", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "buttons", "name", "iconURL", "list", "autofocus", "popupRoot", "document", "body", "zIndex", "globalFullSize", "color", "backgroundColor", "fontFamily", "editor<PERSON>s<PERSON><PERSON>", "enter", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "controls", "font", "handleContentChange", "newContent", "isEditing", "setIsEditing", "editor<PERSON><PERSON>", "containerRef", "current", "setTimeout", "editor", "focus", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "addEventListener", "removeEventListener", "children", "sx", "display", "alignItems", "position", "margin", "boxSizing", "transition", "className", "title", "size", "onClick", "disabled", "containers", "length", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "opacity", "pointerEvents", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "slotProps", "borderRadius", "padding", "border", "PopperProps", "modifiers", "options", "boundary", "enabled", "width", "value", "config", "onChange", "e", "stopPropagation", "bottom", "right", "_c2", "_c3", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/RTE/RTESection.tsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo, useCallback } from \"react\";\r\nimport { Box, Popover, Tooltip, Typography, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\n\r\nimport RTE from \"./RTE\";\r\nimport useDrawerStore, { IRTEContainer, TSectionType } from \"../../../../store/drawerStore\";\r\nimport { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from \"@mui/icons-material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n\titems: IRTEContainer;\r\n\tboxRef: React.RefObject<HTMLDivElement>;\r\n\thandleFocus: (id: string) => void;\r\n\thandleeBlur: (id: string) => void;\r\n\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tcurrentRTEFocusedId: string;\r\n\ttoolbarVisibleRTEId?: string | null;\r\n\tsetToolbarVisibleRTEId?: (id: string | null) => void;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n\t(\r\n\t\t{\r\n\t\t\titems: { id, style, rteBoxValue, placeholder },\r\n\t\t\tboxRef,\r\n\t\t\thandleFocus,\r\n\t\t\thandleeBlur,\r\n\r\n\t\t\tisPopoverOpen,\r\n\t\t\tsetIsPopoverOpen,\r\n\t\t\tcurrentRTEFocusedId,\r\n\t\t\ttoolbarVisibleRTEId,\r\n\t\t\tsetToolbarVisibleRTEId,\r\n\t\t},\r\n\t\tref\r\n\t) => {\r\n\t\tconst { t: translate } = useTranslation();\r\n\t\tconst {\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tsetHtmlContent,\r\n\t\t\ttextvaluess,\r\n\t\t\tsetTextvaluess,\r\n\t\t\tbackgroundC,\r\n\t\t\tsetBackgroundC,\r\n\t\t\tBbordercolor,\r\n\t\t\tBborderSize,\r\n\t\t\tbpadding,\r\n\t\t\tsectionColor,\r\n\t\t\tsetSectionColor,\r\n\t\t\thandleTooltipRTEBlur,\r\n\t\t\thandleTooltipRTEValue,\r\n\t\t\thandleRTEDeleteSection,\r\n\t\t\thandleRTECloneSection,\r\n\t\t\ttooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t} = useDrawerStore((state) => state);\r\n\t\t// Removed unused state variables since we're using Jodit editor directly\r\n\r\n\t\t// Toggle toolbar visibility function\r\n\t\tconst toggleToolbar = (rteId: string) => {\r\n\t\t\tif (setToolbarVisibleRTEId) {\r\n\t\t\t\tif (toolbarVisibleRTEId === rteId) {\r\n\t\t\t\t\tsetToolbarVisibleRTEId(null);\r\n\t\t\t\t\tsetIsPopoverOpen(false);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tsetToolbarVisibleRTEId(rteId);\r\n\t\t\t\t\tsetIsPopoverOpen(true);\r\n\t\t\t\t\thandleFocus(rteId);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Memoize Jodit config to prevent re-renders and focus loss\r\n\t\tconst joditConfig = useMemo((): any => ({\r\n\t\t\treadonly: false,\r\n\t\t\ttoolbar: toolbarVisibleRTEId === id,\r\n\t\t\ttoolbarSticky: false,\r\n\t\t\ttoolbarAdaptive: false,\r\n\t\t\tpastePlain: true,\r\n\t\t\taskBeforePasteHTML: false,\r\n\t\t\taskBeforePasteFromWord: false,\r\n\t\t\t pastePlain: true,             \r\n  askBeforePasteHTML: false,    \r\n  askBeforePasteFromWord: false,\r\n\t\t\tbuttons: [\r\n\t\t\t\t'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n\t\t\t\t'font', 'fontsize', 'link',\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'more',\r\n\t\t\t\t\ticonURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n\t\t\t\t\tlist: [\r\n\t\t\t\t\t\t'source', 'image', 'video', 'table',\r\n\t\t\t\t\t\t'align', 'undo', 'redo', '|',\r\n\t\t\t\t\t\t'hr', 'eraser', 'copyformat',\r\n\t\t\t\t\t\t'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n\t\t\t\t\t\t'outdent', 'indent', 'paragraph',\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tautofocus: false,\r\n\t\t\t// Fix dialog positioning by setting popup root to document body\r\n\t\t\tpopupRoot: document.body,\r\n\t\t\t// Ensure dialogs appear in correct position\r\n\t\t\tzIndex: 100000,\r\n\t\t\tglobalFullSize: false,\r\n\t\t\t// Add custom CSS to ensure text is visible\r\n\t\t\tstyle: {\r\n\t\t\t\tcolor: '#000000 !important',\r\n\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\tfontFamily: 'Poppins, sans-serif',\r\n\t\t\t},\r\n\t\t\t// Override editor styles to ensure text visibility\r\n\t\t\teditorCssClass: 'jodit-tooltip-editor',\r\n\t\t\t// Set default content styling\r\n\t\t\tenter: 'p' as const,\r\n\t\t\t// Fix link dialog positioning\r\n\t\t\tlink: {\r\n\t\t\t\tfollowOnDblClick: false,\r\n\t\t\t\tprocessVideoLink: true,\r\n\t\t\t\tprocessPastedLink: true,\r\n\t\t\t\topenInNewTabCheckbox: true,\r\n\t\t\t\tnoFollowCheckbox: false,\r\n\t\t\t\tmodeClassName: 'input' as const,\r\n\t\t\t},\r\n\t\t\t// Dialog configuration\r\n\t\t\tdialog: {\r\n\t\t\t\tzIndex: 100001,\r\n\t\t\t},\r\n\t\t\tcontrols: {\r\n\t\t\t\tfont: {\r\n\t\t\t\t\tlist: {\r\n\t\t\t\t\t\t\"Poppins, sans-serif\": \"Poppins\",\r\n\t\t\t\t\t\t\"Roboto, sans-serif\": \"Roboto\",\r\n\t\t\t\t\t\t\"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n\t\t\t\t\t\t\"Open Sans, sans-serif\": \"Open Sans\",\r\n\t\t\t\t\t\t\"Calibri, sans-serif\": \"Calibri\",\r\n\t\t\t\t\t\t\"Century Gothic, sans-serif\": \"Century Gothic\",\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}), [toolbarVisibleRTEId, id]);\r\n\r\n\t\t// Memoize onChange handler to prevent re-renders\r\n\t\tconst handleContentChange = useCallback((newContent: string) => {\r\n\t\t\thandleTooltipRTEValue(id, newContent);\r\n\t\t}, [id, handleTooltipRTEValue]);\r\n\t\tconst [isEditing, setIsEditing] = useState(false);\r\n\t\tconst editorRef = useRef(null);\r\n\t\tconst containerRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t\t// const handleInput = () => {\r\n\t\t// \t// Update the content state when user types\r\n\t\t// \tif (boxRef.current) {\r\n\t\t// \t\tconst updatedContent = boxRef.current.innerHTML;\r\n\t\t// \t\tsetContent(updatedContent); // Store the content in state\r\n\t\t// \t\tsetHtmlContent(updatedContent); // Update the HTML content\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t\tpreserveCaretPosition();\r\n\t\t// \t}\r\n\t\t// };\r\n\t\t// Removed caret position functions since we're using Jodit editor\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \t// After content update, restore the cursor position\r\n\t\t// \trestoreCaretPosition();\r\n\t\t// }, [boxRef.current?.innerHTML]); // Run when content changes\r\n\r\n\t\t// Remove section\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \tif (boxRef.current?.innerHTML?.trim()) {\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t}\r\n\t\t// }, [boxRef.current?.innerHTML?.trim()]);\r\n\r\n\t\t// Removed useEffect since we're using Jodit editor directly\r\n\r\n\t\t// Auto-focus the editor when editing mode is activated\r\n\t\tuseEffect(() => {\r\n\t\t\tif (isEditing && editorRef.current) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t(editorRef.current as any).editor.focus();\r\n\t\t\t\t}, 50);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\t// Handle clicks outside the editor to close editing mode\r\n\t\tuseEffect(() => {\r\n\t\t\tconst handleClickOutside = (event: MouseEvent) => {\r\n\t\t\t\tconst isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n\t\t\t\tconst isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n\t\t\t\t// Check if the target is inside the editor or related elements\r\n\t\t\t\tif (\r\n\t\t\t\t\tcontainerRef.current &&\r\n\t\t\t\t\t!containerRef.current.contains(event.target as Node) && // Click outside the editor container\r\n\t\t\t\t\t!isInsidePopup && // Click outside the popup\r\n\t\t\t\t\t!isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n\t\t\t\t\t!isInsideWorkplacePopup && // Click outside the workplace popup\r\n\t\t\t\t\t!isSelectionMarker && // Click outside selection markers\r\n\t\t\t\t\t!isLinkPopup && // Click outside link input popup\r\n\t\t\t\t\t!isInsideToolbarButton &&// Click outside the toolbar button\r\n\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t!isInsideJoditPopupContent &&\r\n\t\t\t\t\t!isInsideAltTextPopup\r\n\t\t\t\t) {\r\n\t\t\t\t\tsetIsEditing(false); // Close the editor if clicked outside\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tif (isEditing) {\r\n\t\t\t\tdocument.addEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t\treturn () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t//padding: 0,\r\n\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\ttransition: \"border 0.2s ease-in-out\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor || \"defaultColor\",\r\n\t\t\t\t\t\t//border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\r\n\t\t\t\t\t\t// padding: `${bpadding}px !important` || \"0\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tclassName=\"qadpt-rte\"\r\n\t\t\t\t\tid=\"rte-box\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\ttitle={\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleRTEDeleteSection(id)}\r\n\t\t\t\t\t\t\t\t\tdisabled={\r\n\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleRTECloneSection(id)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t}}/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tplacement=\"top\"\r\n\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"white\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\tpadding: '0px 4px',\r\n\t\t\t\t\t\t\t\t\tborder: \"1px dashed var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\tmodifiers: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tname: \"preventOverflow\",\r\n\t\t\t\t\t\t\t\t\toptions: {\r\n\t\t\t\t\t\t\t\t\t\tboundary: \"viewport\", // Ensure tooltip doesn't go outside the viewport\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tname: \"flip\",\r\n\t\t\t\t\t\t\t\t\toptions: {\r\n\t\t\t\t\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tcolor: \"#000000\",\r\n\t\t\t\t\t\t\tbackgroundColor: \"#ffffff\"\r\n\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t<style>\r\n\t\t\t\t\t\t\t\t{`\r\n\t\t\t\t\t\t\t\t\t/* Tooltip/Hotspot specific Jodit editor styles */\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg p {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg div {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg span {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-tooltip-editor .jodit-wysiwyg br {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Override any inherited styles from tooltip/modal */\r\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-container .jodit-wysiwyg * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Ensure text is visible in all states */\r\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-wysiwyg[contenteditable=\"true\"] * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Override any modal or tooltip text color inheritance */\r\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg,\r\n\t\t\t\t\t\t\t\t\t.MuiTooltip-tooltip .jodit-wysiwyg * {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #000000 !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Fix Jodit dialog positioning - target correct classes */\r\n\t\t\t\t\t\t\t\t\t.jodit.jodit-dialog {\r\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\r\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t.jodit-dialog .jodit-dialog__panel {\r\n\t\t\t\t\t\t\t\t\t\tposition: relative !important;\r\n\t\t\t\t\t\t\t\t\t\ttop: auto !important;\r\n\t\t\t\t\t\t\t\t\t\tleft: auto !important;\r\n\t\t\t\t\t\t\t\t\t\ttransform: none !important;\r\n\t\t\t\t\t\t\t\t\t\tmax-width: 400px !important;\r\n\t\t\t\t\t\t\t\t\t\tbackground: white !important;\r\n\t\t\t\t\t\t\t\t\t\tborder: 1px solid #ccc !important;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 4px !important;\r\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Fix for link dialog specifically */\r\n\t\t\t\t\t\t\t\t\t.jodit-dialog_alert {\r\n\t\t\t\t\t\t\t\t\t\tposition: fixed !important;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 100001 !important;\r\n\t\t\t\t\t\t\t\t\t\ttop: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\tleft: 50% !important;\r\n\t\t\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%) !important;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t`}\r\n\t\t\t\t\t\t\t</style>\r\n\t\t\t\t\t\t\t<JoditEditor\r\n\t\t\t\t\t\t\t\tvalue={rteBoxValue || \"\"}\r\n\t\t\t\t\t\t\t\tconfig={joditConfig}\r\n\t\t\t\t\t\t\t\tonChange={handleContentChange}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{/* Edit icon for toolbar toggle - positioned at bottom right */}\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\ttoggleToolbar(id);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\tbottom: \"2px\",\r\n\t\t\t\t\t\t\t\t\tright: \"2px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(255, 255, 255, 1)\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\"& svg\": {\r\n\t\t\t\t\t\t\t\t\t\twidth: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"16px\",\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Toggle Toolbar\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: editicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: '16px', width: '16px' }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</Box>\r\n\t\t\t</>\r\n\t\t);\r\n\t}\r\n);\r\n\r\nexport default memo(RTEsection);\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAaC,IAAI,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC7G,SAASC,GAAG,EAAWC,OAAO,EAAcC,UAAU,QAAQ,eAAe;AAC7E,OAAOC,WAAW,MAAM,aAAa;AAGrC,OAAOC,cAAc,MAAuC,+BAA+B;AAI3F,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,gCAAgC;AAC/E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAe/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGnB,UAAU,CAAAoB,EAAA,GAAAD,EAAA,CACvD,CACC;EACCE,KAAK,EAAE;IAAEC,EAAE;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAY,CAAC;EAC9CC,MAAM;EACNC,WAAW;EACXC,WAAW;EAEXC,aAAa;EACbC,gBAAgB;EAChBC,mBAAmB;EACnBC,mBAAmB;EACnBC;AACD,CAAC,EACDC,GAAG,KACC;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAAArB,EAAA;EACJ,MAAM;IAAEsB,CAAC,EAAEC;EAAU,CAAC,GAAG7B,cAAc,CAAC,CAAC;EACzC,MAAM;IACL8B,mBAAmB;IACnBC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,QAAQ;IACRC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,qBAAqB;IACrBC,sBAAsB;IACtBC,qBAAqB;IACrBC,OAAO;IACPC,WAAW;IACXC;EACD,CAAC,GAAGnD,cAAc,CAAEoD,KAAK,IAAKA,KAAK,CAAC;EACpC;;EAEA;EACA,MAAMC,aAAa,GAAIC,KAAa,IAAK;IACxC,IAAI9B,sBAAsB,EAAE;MAC3B,IAAID,mBAAmB,KAAK+B,KAAK,EAAE;QAClC9B,sBAAsB,CAAC,IAAI,CAAC;QAC5BH,gBAAgB,CAAC,KAAK,CAAC;MACxB,CAAC,MAAM;QACNG,sBAAsB,CAAC8B,KAAK,CAAC;QAC7BjC,gBAAgB,CAAC,IAAI,CAAC;QACtBH,WAAW,CAACoC,KAAK,CAAC;MACnB;IACD;EACD,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG7D,OAAO,CAAC,OAAY;IACvC8D,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAElC,mBAAmB,KAAKV,EAAE;IACnC6C,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC5BF,UAAU,EAAE,IAAI;IAClBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC5BC,OAAO,EAAE,CACR,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACCC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACL,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACnC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAElC,CAAC,CACD;IACDC,SAAS,EAAE,KAAK;IAChB;IACAC,SAAS,EAAEC,QAAQ,CAACC,IAAI;IACxB;IACAC,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrB;IACA1D,KAAK,EAAE;MACN2D,KAAK,EAAE,oBAAoB;MAC3BC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE;IACb,CAAC;IACD;IACAC,cAAc,EAAE,sBAAsB;IACtC;IACAC,KAAK,EAAE,GAAY;IACnB;IACAC,IAAI,EAAE;MACLC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IAChB,CAAC;IACD;IACAC,MAAM,EAAE;MACPd,MAAM,EAAE;IACT,CAAC;IACDe,QAAQ,EAAE;MACTC,IAAI,EAAE;QACLrB,IAAI,EAAE;UACL,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAC/B;MACD;IACD;EACD,CAAC,CAAC,EAAE,CAAC3C,mBAAmB,EAAEV,EAAE,CAAC,CAAC;;EAE9B;EACA,MAAM2E,mBAAmB,GAAG7F,WAAW,CAAE8F,UAAkB,IAAK;IAC/D3C,qBAAqB,CAACjC,EAAE,EAAE4E,UAAU,CAAC;EACtC,CAAC,EAAE,CAAC5E,EAAE,EAAEiC,qBAAqB,CAAC,CAAC;EAC/B,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMuG,SAAS,GAAGpG,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqG,YAAY,GAAGrG,MAAM,CAAwB,IAAI,CAAC;;EAExD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACAF,SAAS,CAAC,MAAM;IACf,IAAIoG,SAAS,IAAIE,SAAS,CAACE,OAAO,EAAE;MACnCC,UAAU,CAAC,MAAM;QACfH,SAAS,CAACE,OAAO,CAASE,MAAM,CAACC,KAAK,CAAC,CAAC;MAC1C,CAAC,EAAE,EAAE,CAAC;IACP;EACD,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;;EAEf;EACApG,SAAS,CAAC,MAAM;IACf,MAAM4G,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjD,MAAMC,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAG/B,QAAQ,CAACwC,aAAa,CAAC,cAAc,CAAC,cAAAT,qBAAA,uBAAtCA,qBAAA,CAAwCU,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMM,kBAAkB,IAAAV,sBAAA,GAAGhC,QAAQ,CAACwC,aAAa,CAAC,gBAAgB,CAAC,cAAAR,sBAAA,uBAAxCA,sBAAA,CAA0CS,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMO,sBAAsB,GAAGD,kBAAkB,MAAAT,sBAAA,GAAIjC,QAAQ,CAACwC,aAAa,CAAC,sBAAsB,CAAC,cAAAP,sBAAA,uBAA9CA,sBAAA,CAAgDQ,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMQ,iBAAiB,GAAId,KAAK,CAACM,MAAM,CAAiB5F,EAAE,CAACqG,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAZ,sBAAA,GAAGlC,QAAQ,CAACwC,aAAa,CAAC,wBAAwB,CAAC,cAAAN,sBAAA,uBAAhDA,sBAAA,CAAkDO,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMW,qBAAqB,GAAIjB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMW,cAAc,GAAIlB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,IACCb,YAAY,CAACC,OAAO,IACpB,CAACD,YAAY,CAACC,OAAO,CAACgB,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MAAI;MACxD,CAACG,aAAa;MAAI;MAClB,CAACG,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACE,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAG;MACzB,CAACC,cAAc,IACf,CAACb,yBAAyB,IAC1B,CAACG,oBAAoB,EACpB;QACDhB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;MACtB;IACD,CAAC;IAED,IAAID,SAAS,EAAE;MACdrB,QAAQ,CAACiD,gBAAgB,CAAC,WAAW,EAAEpB,kBAAkB,CAAC;MAC1D,OAAO,MAAM7B,QAAQ,CAACkD,mBAAmB,CAAC,WAAW,EAAErB,kBAAkB,CAAC;IAC3E;EACD,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EAEf,oBACCpF,OAAA,CAAAE,SAAA;IAAAgH,QAAA,eACClH,OAAA,CAACV,GAAG;MACH6H,EAAE,EAAE;QACHC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,UAAU;QACpB;QACAC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,UAAU,EAAE,yBAAyB;QACrCrD,eAAe,EAAE/B,YAAY,IAAI;QACjC;QACA;MACD,CAAE;MACFqF,SAAS,EAAC,WAAW;MACrBnH,EAAE,EAAC,SAAS;MAAA2G,QAAA,eAEZlH,OAAA,CAACT,OAAO;QACPoI,KAAK,eACJ3H,OAAA,CAAAE,SAAA;UAAAgH,QAAA,gBACClH,OAAA,CAACR,UAAU;YACVoI,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMpF,sBAAsB,CAAClC,EAAE,CAAE;YAC1CuH,QAAQ,EACP,EAAA1G,qBAAA,GAAAyB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAxB,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuC2G,UAAU,cAAA1G,sBAAA,uBAAjDA,sBAAA,CAAmD2G,MAAM,MAAK,CAC9D;YACDb,EAAE,EAAE;cACH,SAAS,EAAE;gBACV/C,eAAe,EAAE;cAClB,CAAC;cACD6D,GAAG,EAAE;gBACJC,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YAAAjB,QAAA,eAEFlH,OAAA;cACCoI,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzI;cAAW,CAAE;cAChDY,KAAK,EAAE;gBACN8H,OAAO,EACN,EAAAhH,sBAAA,GAAAuB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAtB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCyG,UAAU,cAAAxG,sBAAA,uBAAjDA,sBAAA,CAAmDyG,MAAM,MAAK,CAAC,GAC5D,GAAG,GACH,CAAC;gBACLO,aAAa,EAAE,MAAM;gBACrBC,MAAM,EAAE;cACT;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACb5I,OAAA,CAACR,UAAU;YACVoI,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAACnC,EAAE,CAAE;YACzC4G,EAAE,EAAE;cACH,SAAS,EAAE;gBACV/C,eAAe,EAAE;cAClB,CAAC;cACD6D,GAAG,EAAE;gBACJO,MAAM,EAAE,MAAM;gBACdN,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YAAAjB,QAAA,eAEFlH,OAAA;cAAMoI,uBAAuB,EAAE;gBAAEC,MAAM,EAAE1I;cAAS,CAAE;cACpDa,KAAK,EAAE;gBACN8H,OAAO,EACN,EAAA9G,sBAAA,GAAAqB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAApB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCuG,UAAU,cAAAtG,sBAAA,uBAAjDA,sBAAA,CAAmDuG,MAAM,MAAK,CAAC,GAC5D,GAAG,GACH,CAAC;gBACLO,aAAa,EAAE,MAAM;gBACrBC,MAAM,EAAE;cACT;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA,eACZ,CACF;QACDC,SAAS,EAAC,KAAK;QACfC,SAAS,EAAE;UACVnG,OAAO,EAAE;YACRwE,EAAE,EAAE;cACH/C,eAAe,EAAE,OAAO;cACxBD,KAAK,EAAE,OAAO;cACd4E,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE;YACT;UACD;QACD,CAAE;QACFC,WAAW,EAAE;UACZC,SAAS,EAAE,CACV;YACCzF,IAAI,EAAE,iBAAiB;YACvB0F,OAAO,EAAE;cACRC,QAAQ,EAAE,UAAU,CAAE;YACvB;UACD,CAAC,EACD;YACC3F,IAAI,EAAE,MAAM;YACZ0F,OAAO,EAAE;cACRE,OAAO,EAAE;YACV;UACD,CAAC;QAEH,CAAE;QAAApC,QAAA,eAEFlH,OAAA;UAAKQ,KAAK,EAAE;YACX+I,KAAK,EAAE,MAAM;YACbjC,QAAQ,EAAE,UAAU;YACpBnD,KAAK,EAAE,SAAS;YAChBC,eAAe,EAAE;UAClB,CAAE;UAAA8C,QAAA,gBACDlH,OAAA;YAAAkH,QAAA,EACE;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UAAS;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACR5I,OAAA,CAACP,WAAW;YACX+J,KAAK,EAAE/I,WAAW,IAAI,EAAG;YACzBgJ,MAAM,EAAExG,WAAY;YACpByG,QAAQ,EAAExE;UAAoB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAEF5I,OAAA,CAACR,UAAU;YACVoI,IAAI,EAAC,OAAO;YACZC,OAAO,EAAG8B,CAAC,IAAK;cACfA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB7G,aAAa,CAACxC,EAAE,CAAC;YAClB,CAAE;YACF4G,EAAE,EAAE;cACHG,QAAQ,EAAE,UAAU;cACpBuC,MAAM,EAAE,KAAK;cACbC,KAAK,EAAE,KAAK;cACZP,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,MAAM;cACdpE,eAAe,EAAE,0BAA0B;cAC3CH,MAAM,EAAE,IAAI;cACZ,SAAS,EAAE;gBACVG,eAAe,EAAE;cAClB,CAAC;cACD,OAAO,EAAE;gBACRmF,KAAK,EAAE,MAAM;gBACbf,MAAM,EAAE;cACT;YACD,CAAE;YACFb,KAAK,EAAEhG,SAAS,CAAC,gBAAgB,CAAE;YAAAuF,QAAA,eAEnClH,OAAA;cACCoI,uBAAuB,EAAE;gBAAEC,MAAM,EAAExI;cAAS,CAAE;cAC9CW,KAAK,EAAE;gBAAEgI,MAAM,EAAE,MAAM;gBAAEe,KAAK,EAAE;cAAO;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC,gBACL,CAAC;AAEL,CAAC;EAAA,QA5ZyB9I,cAAc,EAoBnCJ,cAAc;AAAA,EAyYpB,CAAC;EAAA,QA7Z0BI,cAAc,EAoBnCJ,cAAc;AAAA,EAyYnB;AAACqK,GAAA,GA7aI5J,UAAqC;AA+a3C,eAAA6J,GAAA,gBAAe7K,IAAI,CAACgB,UAAU,CAAC;AAAC,IAAAE,EAAA,EAAA0J,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA5J,EAAA;AAAA4J,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}