import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo, useCallback } from "react";
import { Box, Popover, Tooltip, Typography, IconButton } from "@mui/material";
import JoditEditor from "jodit-react";

import RTE from "./RTE";
import useDrawerStore, { IRTEContainer, TSectionType } from "../../../../store/drawerStore";
import { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import { copyicon, deleteicon, editicon } from "../../../../assets/icons/icons";
import { useTranslation } from 'react-i18next';

interface RTEsectionProps {
	items: IRTEContainer;
	boxRef: React.RefObject<HTMLDivElement>;
	handleFocus: (id: string) => void;
	handleeBlur: (id: string) => void;

	isPopoverOpen: boolean;
	setIsPopoverOpen: (params: boolean) => void;
	currentRTEFocusedId: string;
	toolbarVisibleRTEId?: string | null;
	setToolbarVisibleRTEId?: (id: string | null) => void;
}

const RTEsection: React.FC<RTEsectionProps> = forwardRef(
	(
		{
			items: { id, style, rteBoxValue, placeholder },
			boxRef,
			handleFocus,
			handleeBlur,

			isPopoverOpen,
			setIsPopoverOpen,
			currentRTEFocusedId,
			toolbarVisibleRTEId,
			setToolbarVisibleRTEId,
		},
		ref
	) => {
		const { t: translate } = useTranslation();
		const {
			setIsUnSavedChanges,
			setHtmlContent,
			textvaluess,
			setTextvaluess,
			backgroundC,
			setBackgroundC,
			Bbordercolor,
			BborderSize,
			bpadding,
			sectionColor,
			setSectionColor,
			handleTooltipRTEBlur,
			handleTooltipRTEValue,
			handleRTEDeleteSection,
			handleRTECloneSection,
			tooltip,
			currentStep,
			toolTipGuideMetaData,
		} = useDrawerStore((state) => state);
		// Removed unused state variables since we're using Jodit editor directly

		// Toggle toolbar visibility function
		const toggleToolbar = (rteId: string) => {
			if (setToolbarVisibleRTEId) {
				if (toolbarVisibleRTEId === rteId) {
					setToolbarVisibleRTEId(null);
					setIsPopoverOpen(false);
				} else {
					setToolbarVisibleRTEId(rteId);
					setIsPopoverOpen(true);
					handleFocus(rteId);
				}
			}
		};

		// Memoize Jodit config to prevent re-renders and focus loss
		const joditConfig = useMemo((): any => ({
			readonly: false,
			toolbar: toolbarVisibleRTEId === id,
			toolbarSticky: false,
			toolbarAdaptive: false,
			pastePlain: true,
			askBeforePasteHTML: false,
			askBeforePasteFromWord: false,
			
			buttons: [
				'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',
				'font', 'fontsize', 'link',
				{
					name: 'more',
					iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',
					list: [
						'source', 'image', 'video', 'table',
						'align', 'undo', 'redo', '|',
						'hr', 'eraser', 'copyformat',
						'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',
						'outdent', 'indent', 'paragraph',
					]
				}
			],
			autofocus: false,
			// Fix dialog positioning by setting popup root to document body
			popupRoot: document.body,
			// Ensure dialogs appear in correct position
			zIndex: 100000,
			globalFullSize: false,
			// Add custom CSS to ensure text is visible
			style: {
				color: '#000000 !important',
				backgroundColor: '#ffffff',
				fontFamily: 'Poppins, sans-serif',
			},
			// Override editor styles to ensure text visibility
			editorCssClass: 'jodit-tooltip-editor',
			// Set default content styling
			enter: 'p' as const,
			// Fix link dialog positioning
			link: {
				followOnDblClick: false,
				processVideoLink: true,
				processPastedLink: true,
				openInNewTabCheckbox: true,
				noFollowCheckbox: false,
				modeClassName: 'input' as const,
			},
			// Dialog configuration
			dialog: {
				zIndex: 100001,
			},
			controls: {
				font: {
					list: {
						"Poppins, sans-serif": "Poppins",
						"Roboto, sans-serif": "Roboto",
						"Comic Sans MS, sans-serif": "Comic Sans MS",
						"Open Sans, sans-serif": "Open Sans",
						"Calibri, sans-serif": "Calibri",
						"Century Gothic, sans-serif": "Century Gothic",
					}
				}
			}
		}), [toolbarVisibleRTEId, id]);

		// Memoize onChange handler to prevent re-renders
		const handleContentChange = useCallback((newContent: string) => {
			handleTooltipRTEValue(id, newContent);
		}, [id, handleTooltipRTEValue]);
		const [isEditing, setIsEditing] = useState(false);
		const editorRef = useRef(null);
		const containerRef = useRef<HTMLDivElement | null>(null);

		// const handleInput = () => {
		// 	// Update the content state when user types
		// 	if (boxRef.current) {
		// 		const updatedContent = boxRef.current.innerHTML;
		// 		setContent(updatedContent); // Store the content in state
		// 		setHtmlContent(updatedContent); // Update the HTML content
		// 		setIsUnSavedChanges(true);
		// 		preserveCaretPosition();
		// 	}
		// };
		// Removed caret position functions since we're using Jodit editor

		// useEffect(() => {
		// 	// After content update, restore the cursor position
		// 	restoreCaretPosition();
		// }, [boxRef.current?.innerHTML]); // Run when content changes

		// Remove section

		// useEffect(() => {
		// 	if (boxRef.current?.innerHTML?.trim()) {
		// 		setIsUnSavedChanges(true);
		// 	}
		// }, [boxRef.current?.innerHTML?.trim()]);

		// Removed useEffect since we're using Jodit editor directly

		// Auto-focus the editor when editing mode is activated
		useEffect(() => {
			if (isEditing && editorRef.current) {
				setTimeout(() => {
					(editorRef.current as any).editor.focus();
				}, 50);
			}
		}, [isEditing]);

		// Handle clicks outside the editor to close editing mode
		useEffect(() => {
			const handleClickOutside = (event: MouseEvent) => {
				const isInsideJoditPopupContent = (event.target as HTMLElement).closest(".jodit-popup__content") !== null;
				const isInsideAltTextPopup = (event.target as HTMLElement).closest(".jodit-ui-input") !== null;
				const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
				const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
				const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
				const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
				const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
				const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
				const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;

				// Check if the target is inside the editor or related elements
				if (
					containerRef.current &&
					!containerRef.current.contains(event.target as Node) && // Click outside the editor container
					!isInsidePopup && // Click outside the popup
					!isInsideJoditPopup && // Click outside the WYSIWYG editor
					!isInsideWorkplacePopup && // Click outside the workplace popup
					!isSelectionMarker && // Click outside selection markers
					!isLinkPopup && // Click outside link input popup
					!isInsideToolbarButton &&// Click outside the toolbar button
					!isInsertButton &&
					!isInsideJoditPopupContent &&
					!isInsideAltTextPopup
				) {
					setIsEditing(false); // Close the editor if clicked outside
				}
			};

			if (isEditing) {
				document.addEventListener("mousedown", handleClickOutside);
				return () => document.removeEventListener("mousedown", handleClickOutside);
			}
		}, [isEditing]);

		return (
			<>
				<Box
					sx={{
						display: "flex",
						alignItems: "center",
						position: "relative",
						//padding: 0,
						margin: 0,
						boxSizing: "border-box",
						transition: "border 0.2s ease-in-out",
						backgroundColor: sectionColor || "defaultColor",
						//border: `${BborderSize}px solid ${Bbordercolor} !important` || "defaultColor",
						// padding: `${bpadding}px !important` || "0",
					}}
					className="qadpt-rte"
					id="rte-box"
				>
					<Tooltip
						title={
							<>
								<IconButton
									size="small"
									onClick={() => handleRTEDeleteSection(id)}
									disabled={
										toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1
									}
									sx={{
										"&:hover": {
											backgroundColor: "transparent !important",
										},
										svg: {
											path: {
												fill:"var(--primarycolor)"
											}
										}
									}}
								>
									<span
										dangerouslySetInnerHTML={{ __html: deleteicon }}
										style={{
											opacity:
												toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1
													? 0.5
													: 1,
											pointerEvents: 'none',
											height: "24px",
										}}
									/>
								</IconButton>
								<IconButton
									size="small"
									onClick={() => handleRTECloneSection(id)}
									sx={{
										"&:hover": {
											backgroundColor: "transparent !important",
										},
										svg: {
											height: "24px",
											path: {
												fill:"var(--primarycolor)"
											}
										}
									}}
								>
									<span dangerouslySetInnerHTML={{ __html: copyicon }}
									style={{
										opacity:
											toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1
												? 0.5
												: 1,
										pointerEvents: 'none',
										height: "24px",
									}}/>
								</IconButton>
							</>
						}
						placement="top"
						slotProps={{
							tooltip: {
								sx: {
									backgroundColor: "white",
									color: "black",
									borderRadius: "4px",
									padding: '0px 4px',
									border: "1px dashed var(--primarycolor)",
								},
							},
						}}
						PopperProps={{
							modifiers: [
								{
									name: "preventOverflow",
									options: {
										boundary: "viewport", // Ensure tooltip doesn't go outside the viewport
									},
								},
								{
									name: "flip",
									options: {
										enabled: true,
									},
								},
							],
						}}
					>
						<div style={{
							width: "100%",
							position: "relative",
							color: "#000000",
							backgroundColor: "#ffffff"
						}}>
							<style>
								{`
									/* Tooltip/Hotspot specific Jodit editor styles */
									.jodit-tooltip-editor .jodit-wysiwyg {
										color: #000000 !important;
										background-color: #ffffff !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg p {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg * {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg div {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg span {
										color: #000000 !important;
									}
									.jodit-tooltip-editor .jodit-wysiwyg br {
										color: #000000 !important;
									}
									/* Override any inherited styles from tooltip/modal */
									.jodit-container .jodit-wysiwyg {
										color: #000000 !important;
										background-color: #ffffff !important;
									}
									.jodit-container .jodit-wysiwyg * {
										color: #000000 !important;
									}
									/* Ensure text is visible in all states */
									.jodit-wysiwyg[contenteditable="true"] {
										color: #000000 !important;
										background-color: #ffffff !important;
									}
									.jodit-wysiwyg[contenteditable="true"] * {
										color: #000000 !important;
									}
									/* Override any modal or tooltip text color inheritance */
									.MuiTooltip-tooltip .jodit-wysiwyg,
									.MuiTooltip-tooltip .jodit-wysiwyg * {
										color: #000000 !important;
									}
									/* Fix Jodit dialog positioning - target correct classes */
									.jodit.jodit-dialog {
										position: fixed !important;
										z-index: 100001 !important;
										top: 50% !important;
										left: 50% !important;
										transform: translate(-50%, -50%) !important;
									}
									.jodit-dialog .jodit-dialog__panel {
										position: relative !important;
										top: auto !important;
										left: auto !important;
										transform: none !important;
										max-width: 400px !important;
										background: white !important;
										border: 1px solid #ccc !important;
										border-radius: 4px !important;
										box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
									}
									/* Fix for link dialog specifically */
									.jodit-dialog_alert {
										position: fixed !important;
										z-index: 100001 !important;
										top: 50% !important;
										left: 50% !important;
										transform: translate(-50%, -50%) !important;
									}
								`}
							</style>
							<JoditEditor
								value={rteBoxValue || ""}
								config={joditConfig}
								onChange={handleContentChange}
							/>
							{/* Edit icon for toolbar toggle - positioned at bottom right */}
							<IconButton
								size="small"
								onClick={(e) => {
									e.stopPropagation();
									toggleToolbar(id);
								}}
								sx={{
									position: "absolute",
									bottom: "2px",
									right: "2px",
									width: "24px",
									height: "24px",
									backgroundColor: "rgba(255, 255, 255, 0.9)",
									zIndex: 1000,
									"&:hover": {
										backgroundColor: "rgba(255, 255, 255, 1)",
									},
									"& svg": {
										width: "16px",
										height: "16px",
									}
								}}
								title={translate("Toggle Toolbar")}
							>
								<span
									dangerouslySetInnerHTML={{ __html: editicon }}
									style={{ height: '16px', width: '16px' }}
								/>
							</IconButton>
						</div>
					</Tooltip>
				</Box>
			</>
		);
	}
);

export default memo(RTEsection);
